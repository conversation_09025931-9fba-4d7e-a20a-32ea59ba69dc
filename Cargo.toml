[workspace]
members = ["kernel", "boot", "config"]
resolver = "2"

[workspace.dependencies]
# 内核开发常用依赖
spin = { version = "0.9", default-features = false }
lazy_static = { version = "1.4", features = ["spin_no_std"] }
bitflags = "2.4"
linked_list_allocator = "0.10"
x86_64 = "0.14"
uart_16550 = "0.3"
pic8259 = "0.10"
pc-keyboard = "0.7"
volatile = "0.4"
cfg-if = "1.0.1"


[profile.dev]
panic = "abort"

[profile.release]
panic = "abort"
lto = true
codegen-units = 1
