# Echos Kernel

一个支持 RISC-V 64 和 LoongArch64 架构的实验性内核项目。

## 架构支持

- **RISC-V 64**: 使用 `riscv64gc-unknown-none-elf` 目标
- **LoongArch64**: 使用 `loongarch64-unknown-none` 目标

## 项目结构

```
Echos/
├── kernel/          # 内核主体
├── bootloader/      # 引导加载程序
├── common/          # 共享库
├── .cargo/          # Cargo 配置
├── Makefile         # 构建脚本
└── README.md        # 项目文档
```

## 快速开始

### 环境准备

1. 安装 Rust 工具链：
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

2. 安装目标架构支持：
```bash
# RISC-V 64
rustup target add riscv64gc-unknown-none-elf

# LoongArch64 (需要 nightly)
rustup toolchain install nightly
rustup +nightly target add loongarch64-unknown-none
```

3. 安装必要工具：
```bash
# 安装 QEMU
sudo apt install qemu-system-riscv64 qemu-system-loongarch64

# 安装 Rust 工具
cargo install cargo-binutils
rustup component add llvm-tools-preview
```

### 构建和运行

```bash
# 构建 RISC-V 64 内核
make ARCH=riscv64

# 构建 LoongArch64 内核
make ARCH=loongarch64

# 在 QEMU 中运行 RISC-V 64 内核
make ARCH=riscv64 run

# 在 QEMU 中运行 LoongArch64 内核
make ARCH=loongarch64 run

# 或使用快捷命令
make run-riscv64
make run-loongarch64
```

### 调试

```bash
# 启动调试模式
make ARCH=riscv64 debug

# 在另一个终端启动 GDB
make ARCH=riscv64 gdb
```

## Makefile 目标

- `make kernel` - 构建内核
- `make bootloader` - 构建引导加载程序
- `make run` - 在 QEMU 中运行
- `make debug` - 调试模式运行
- `make clean` - 清理构建文件
- `make test` - 运行测试
- `make fmt` - 格式化代码
- `make clippy` - 运行 Clippy 检查
- `make help` - 显示帮助信息

## 开发说明

### 架构抽象

项目使用 `common` crate 提供架构抽象层，支持：
- 统一的控制台输出接口
- 内存管理
- 中断处理
- 架构特定的初始化

### 内存布局

- **RISC-V 64**: 内核加载到 `0x80200000`
- **LoongArch64**: 内核加载到 `0x90000000`

### 添加新架构

1. 在 `common/src/arch/` 下添加新的架构模块
2. 实现必要的架构接口
3. 更新 `Makefile` 添加新的目标配置
4. 添加相应的链接脚本

## 许可证

MIT License
