[package]
name = "kernel"
version = "0.1.0"
edition = "2021"

[dependencies]
config = { path = "../config" }
spin = { workspace = true }
lazy_static = { workspace = true }
bitflags = { workspace = true }
linked_list_allocator = { workspace = true }
volatile = { workspace = true }
boot = { path = "../boot" }
# RISC-V 特定依赖
[target.'cfg(target_arch = "riscv64")'.dependencies]
riscv = "0.14.0"

# LoongArch64 特定依赖 (目前生态较少，主要使用内联汇编)
[target.'cfg(target_arch = "loongarch64")'.dependencies]

[[bin]]
name = "kernel"
path = "src/main.rs"
