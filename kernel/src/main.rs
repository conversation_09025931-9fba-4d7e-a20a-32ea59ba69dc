#![no_std]
#![no_main]

use boot;


use core::panic::PanicInfo;

#[panic_handler]
fn panic(_info: &PanicInfo) -> ! {
    loop {
        unsafe {
            core::arch::asm!("wfi");
        }
    }
}

#[no_mangle]
pub extern "C" fn rust_main(hart_id: usize, device_tree_addr: usize) -> ! {
    // 内核主入口点
    // TODO: 初始化内核
    loop {
        unsafe {
            core::arch::asm!("wfi");
        }
    }
}
