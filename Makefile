# Echos Kernel Makefile
# 支持 RISC-V 64 和 LoongArch64 架构

# 默认架构
ARCH ?= riscv64

# 架构相关配置
ifeq ($(ARCH), riscv64)
    TARGET = riscv64gc-unknown-none-elf
    QEMU = qemu-system-riscv64
    QEMU_ARGS = -machine virt -cpu rv64 -smp 1 -m 128M
    QEMU_ARGS += -nographic
    QEMU_ARGS += -bios default
    KERNEL_ADDR = 0x80200000
else ifeq ($(ARCH), loongarch64)
    TARGET = loongarch64-unknown-none
    QEMU = qemu-system-loongarch64
    QEMU_ARGS = -machine virt -cpu la464 -smp 1 -m 128M
    QEMU_ARGS += -nographic
    KERNEL_ADDR = 0x90000000
else
    $(error Unsupported architecture: $(ARCH))
endif

# 构建配置
MODE ?= debug
CARGO_MODE = $(if $(filter release,$(MODE)),--release,)
TARGET_DIR = target/$(TARGET)/$(MODE)

# 文件路径
KERNEL_ELF = $(TARGET_DIR)/kernel
KERNEL_BIN = $(TARGET_DIR)/kernel.bin
BOOT_ELF = $(TARGET_DIR)/libboot.rlib
BOOT_BIN = $(TARGET_DIR)/boot.bin

# 工具
OBJCOPY = rust-objcopy
OBJDUMP = rust-objdump

.PHONY: all build kernel boot clean run debug gdb test doc fmt clippy

# 默认目标
all: kernel

# 构建内核
kernel:
	@echo "Building kernel for $(ARCH)..."
	cargo build $(CARGO_MODE) --target $(TARGET) --bin kernel
	$(OBJCOPY) --binary-architecture=$(ARCH) --strip-all -O binary $(KERNEL_ELF) $(KERNEL_BIN)

# 构建引导加载程序
boot:
	@echo "Building boot library for $(ARCH)..."
	cargo build $(CARGO_MODE) --target $(TARGET) --lib -p boot

# 构建所有组件
build: kernel boot

# 运行内核 (使用 QEMU)
run: kernel
	@echo "Starting QEMU for $(ARCH)..."
	$(QEMU) $(QEMU_ARGS) -kernel $(KERNEL_ELF)

# 运行内核并记录日志
runlog: kernel
	@echo "Starting QEMU for $(ARCH) with logging..."
	@mkdir -p logs
	$(QEMU) $(QEMU_ARGS) -kernel $(KERNEL_ELF) \
		-d guest_errors,in_asm \
		-D logs/qemu-$(ARCH).log

# 调试模式运行
debug: kernel
	@echo "Starting QEMU in debug mode for $(ARCH)..."
	$(QEMU) $(QEMU_ARGS) -kernel $(KERNEL_ELF) -s -S

# GDB 调试
gdb: kernel
	@echo "Starting GDB session..."
	@echo "Connect with: target remote :1234"
	gdb-multiarch $(KERNEL_ELF)

# 反汇编内核
disasm: kernel
	$(OBJDUMP) -d $(KERNEL_ELF) > $(TARGET_DIR)/kernel.asm
	@echo "Disassembly saved to $(TARGET_DIR)/kernel.asm"

# 查看内核信息
info: kernel
	@echo "=== Kernel Information ==="
	@echo "Architecture: $(ARCH)"
	@echo "Target: $(TARGET)"
	@echo "Mode: $(MODE)"
	@echo "Kernel ELF: $(KERNEL_ELF)"
	@echo "Kernel Binary: $(KERNEL_BIN)"
	@echo ""
	@echo "=== File Sizes ==="
	@ls -lh $(KERNEL_ELF) $(KERNEL_BIN) 2>/dev/null || true
	@echo ""
	@echo "=== ELF Headers ==="
	@readelf -h $(KERNEL_ELF) 2>/dev/null || echo "readelf not available"

# 运行测试
test:
	cargo test --workspace

# 生成文档
doc:
	cargo doc --workspace --no-deps --open

# 代码格式化
fmt:
	cargo fmt --all

# Clippy 检查
clippy:
	cargo clippy --workspace --all-targets -- -D warnings

# 清理构建文件
clean:
	cargo clean
	@echo "Build artifacts cleaned"

# 架构特定的快捷目标
riscv64:
	$(MAKE) ARCH=riscv64 kernel

loongarch64:
	$(MAKE) ARCH=loongarch64 kernel

run-riscv64:
	$(MAKE) ARCH=riscv64 run

run-loongarch64:
	$(MAKE) ARCH=loongarch64 run

# 创建启动脚本
scripts:
	@mkdir -p scripts
	@echo '#!/bin/bash' > scripts/run-riscv64.sh
	@echo 'make ARCH=riscv64 run' >> scripts/run-riscv64.sh
	@echo '#!/bin/bash' > scripts/run-loongarch64.sh
	@echo 'make ARCH=loongarch64 run' >> scripts/run-loongarch64.sh
	@chmod +x scripts/*.sh
	@echo "Scripts created in scripts/ directory"

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@which cargo > /dev/null || (echo "Error: cargo not found" && exit 1)
	@which $(QEMU) > /dev/null || (echo "Warning: $(QEMU) not found" && exit 1)
	@which $(OBJCOPY) > /dev/null || (echo "Warning: $(OBJCOPY) not found" && exit 1)
	@echo "Dependencies check passed"

# 帮助信息
help:
	@echo "Echos Kernel Build System"
	@echo ""
	@echo "Usage: make [target] [ARCH=riscv64|loongarch64] [MODE=debug|release]"
	@echo ""
	@echo "Targets:"
	@echo "  all          - Build kernel (default)"
	@echo "  kernel       - Build kernel only"
	@echo "  boot         - Build boot library only"
	@echo "  build        - Build all components"
	@echo "  run          - Run kernel in QEMU"
	@echo "  runlog       - Run kernel in QEMU with detailed logging"
	@echo "  debug        - Run kernel in QEMU debug mode"
	@echo "  gdb          - Start GDB debugging session"
	@echo "  disasm       - Generate disassembly"
	@echo "  info         - Show kernel information"
	@echo "  test         - Run tests"
	@echo "  doc          - Generate documentation"
	@echo "  fmt          - Format code"
	@echo "  clippy       - Run clippy linter"
	@echo "  clean        - Clean build artifacts"
	@echo "  scripts      - Create helper scripts"
	@echo "  check-deps   - Check required dependencies"
	@echo ""
	@echo "Architecture shortcuts:"
	@echo "  riscv64      - Build for RISC-V 64"
	@echo "  loongarch64  - Build for LoongArch64"
	@echo "  run-riscv64  - Run RISC-V 64 kernel"
	@echo "  run-loongarch64 - Run LoongArch64 kernel"
	@echo ""
	@echo "Examples:"
	@echo "  make ARCH=riscv64 MODE=release run"
	@echo "  make loongarch64"
	@echo "  make run-riscv64"
