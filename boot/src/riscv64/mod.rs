
use config::VIRT_ADDR_START;
use riscv::register::satp;
use core::arch::naked_asm;

#[no_mangle]
pub extern "C" fn kernel_main(hart_id: usize, device_tree_addr: usize) -> ! {
    unsafe extern "C"
    {
        pub fn rust_main(hart_id: usize, device_tree_addr: usize) -> !;
    }
    unsafe {
        rust_main(hart_id, device_tree_addr);
    }
}

#[link_section = ".data.boot_page_table"]
static mut BOOT_PT: [usize; 512] = [0; 512];

#[unsafe(naked)]
#[no_mangle]
#[link_section = ".text.entry"]
unsafe extern "C" fn _start() -> !
{
    naked_asm!(
        // 定义栈空间
        "   .align  12
            .global bootstack
        bootstack:
            .space  0x10000 * 5
            .global bstack_top
        bstack_top:

        // 1. Set Stack Pointer.
        // sp = bootstack + (hartid + 1) * 0x10000
            mv      s0, a0
            mv      s1, a1
            la      sp, bstack_top
            li      t0, {virt_addr_start}
            not     t0, t0
            and     sp, sp, t0

            call    {init_boot_page_table}
            call    {init_mmu}

            li      t0, {virt_addr_start}   // add virtual address
            or      sp, sp, t0

            la      a2, {entry}
            or      a2, a2, t0
            mv      a0, s0
            mv      a1, s1
            jalr    a2                      // call rust_main
        ",
        init_boot_page_table = sym init_boot_page_table,
        init_mmu = sym init_mmu,
        entry = sym kernel_main,
        virt_addr_start = const VIRT_ADDR_START,
    )
}


unsafe extern "C" fn init_mmu() {
    let ptr = (&raw mut BOOT_PT) as usize;
    satp::set(satp::Mode::Sv39, 0, ptr >> 12);
}
    
#[no_mangle]
pub fn init_boot_page_table() {
    unsafe {
        let boot_pt = &mut BOOT_PT;
        // SV39 页表项标志位
        // V=1, R=1, W=1, X=1, A=1, D=1
        let flags = (1 << 0) | (1 << 1) | (1 << 2) | (1 << 3) | (1 << 6) | (1 << 7);
        // 全局标志位 G=1
        let global_flags = flags | (1 << 5);

        for i in 0..0x100 {
            let target_addr = i * 0x4000_0000; // 1GB per page
            let ppn = target_addr >> 12; // 物理页号

            // 0x00000000_00000000 -> 0x00000000_00000000 (256G, 1G PerPage)
            boot_pt[i] = (ppn << 10) | flags;

            // 0xffffffc0_00000000 -> 0x00000000_00000000 (256G, 1G PerPage)
            boot_pt[i + 0x100] = (ppn << 10) | global_flags;
        }
    }
}